// Pharos Network Configuration (Updated from official docs)
export const PHA<PERSON><PERSON>_CONFIG = {
  TESTNET: {
    chainId: 688688, // Confirmed from docs
    name: 'Pharos Testnet',
    rpcUrl: 'https://testnet.dplabs-internal.com', // From original prompt
    explorerUrl: 'https://explorer.pharos.network',
    currency: {
      name: 'PHRS', // Native token is PHRS based on WPHRS in docs
      symbol: 'PHRS',
      decimals: 18,
    },
  },
  MAINNET: {
    chainId: 688689, // Assumed mainnet chain ID
    name: 'Pharos Mainnet',
    rpcUrl: 'https://mainnet.pharos.network',
    explorerUrl: 'https://explorer.pharos.network',
    currency: {
      name: 'PHRS',
      symbol: 'PHRS',
      decimals: 18,
    },
  },
} as const;

// Known Pharos Testnet Contracts (from official docs)
export const PHAROS_TESTNET_CONTRACTS = {
  WPHRS: '******************************************',
  USDC: '******************************************',
  USDT: '******************************************',
  WBTC: '******************************************',
  WETH: '******************************************',
  MULTICALL3: '******************************************',
  CREATE2_DEPLOYER: '******************************************',
  PERMIT2: '******************************************',
} as const;

// Contract addresses (akan diupdate setelah deployment)
export const CONTRACT_ADDRESSES = {
  TOKEN_LAUNCHER: import.meta.env.VITE_TOKEN_LAUNCHER_ADDRESS || '',
  CURVE_SALE: import.meta.env.VITE_CURVE_SALE_ADDRESS || '',
  DEX_ROUTER: import.meta.env.VITE_DEX_ROUTER_ADDRESS || '',
  WPHAR: import.meta.env.VITE_WPHAR_ADDRESS || '',
} as const;

// Platform configuration
export const PLATFORM_CONFIG = {
  MIN_LAUNCH_FEE: '0.01', // 0.01 PHAR
  MAX_TOKEN_SUPPLY: '1000000000', // 1B tokens
  PLATFORM_FEE_PERCENTAGE: 10, // 10%
  GRADUATION_THRESHOLD: '69', // 69 PHAR
} as const;

// UI Configuration
export const UI_CONFIG = {
  TOKENS_PER_PAGE: 12,
  REFRESH_INTERVAL: 30000, // 30 seconds
  DEBOUNCE_DELAY: 500, // 500ms
} as const;

// Token metadata template
export const TOKEN_TEMPLATE = {
  name: '',
  symbol: '',
  totalSupply: '1000000', // 1M default
  description: '',
  imageUrl: '',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  WALLET_NOT_CONNECTED: 'Please connect your wallet',
  INSUFFICIENT_BALANCE: 'Insufficient balance',
  INVALID_AMOUNT: 'Invalid amount',
  TRANSACTION_FAILED: 'Transaction failed',
  NETWORK_ERROR: 'Network error',
  CONTRACT_ERROR: 'Contract interaction error',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  TOKEN_LAUNCHED: 'Token launched successfully!',
  TOKENS_PURCHASED: 'Tokens purchased successfully!',
  TOKENS_SOLD: 'Tokens sold successfully!',
  WALLET_CONNECTED: 'Wallet connected successfully!',
} as const;
