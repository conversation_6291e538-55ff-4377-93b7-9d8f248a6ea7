{"hash": "9be1188a", "configHash": "dede8aff", "lockfileHash": "4ac28a0e", "browserHash": "7b78de52", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1dc007c6", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "6edf7e38", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a4857ec2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ac6b86db", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "bd325e63", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "9acbc410", "needsInterop": false}, "ethers": {"src": "../../ethers/lib.esm/index.js", "file": "ethers.js", "fileHash": "c9ba5ca0", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "0e722caf", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6f83f849", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4cb55ca9", "needsInterop": false}}, "chunks": {"chunk-VJA3Q2RH": {"file": "chunk-VJA3Q2RH.js"}, "chunk-LYJUZW3I": {"file": "chunk-LYJUZW3I.js"}, "chunk-TJE776R7": {"file": "chunk-TJE776R7.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}