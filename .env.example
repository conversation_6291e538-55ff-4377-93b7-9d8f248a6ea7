# Pharos Network Configuration (Official)
PHAROS_RPC_URL=https://testnet.dplabs-internal.com
PHAROS_CHAIN_ID=688688
PHAROS_EXPLORER_URL=https://explorer.pharos.network

# Known Pharos Testnet Contracts
WPHRS_ADDRESS=0x3019B247381c850ab53Dc0EE53bCe7A07Ea9155f
USDC_ADDRESS=0x72df0bcd7276f2dFbAc900D1CE63c272C4BCcCED
MULTICALL3_ADDRESS=0xd579c2FF374fCf31a380f40fc7876Bea959e42e1

# Private Keys (DO NOT COMMIT REAL KEYS)
DEPLOYER_PRIVATE_KEY=your_deployer_private_key_here
TREASURY_PRIVATE_KEY=your_treasury_private_key_here

# Contract Addresses (Will be populated after deployment)
TOKEN_LAUNCHER_ADDRESS=
CURVE_SALE_ADDRESS=
LIQUIDITY_MANAGER_ADDRESS=
TREASURY_SPLIT_ADDRESS=

# DEX Configuration
DEX_ROUTER_ADDRESS=
DEX_FACTORY_ADDRESS=
WPHAR_ADDRESS=

# Frontend Configuration
VITE_PHAROS_RPC_URL=https://testnet.dplabs-internal.com
VITE_PHAROS_CHAIN_ID=688688
VITE_TOKEN_LAUNCHER_ADDRESS=
VITE_CURVE_SALE_ADDRESS=

# Supabase Configuration (Optional)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# API Keys
COINGECKO_API_KEY=your_coingecko_api_key
MORALIS_API_KEY=your_moralis_api_key

# Platform Configuration
PLATFORM_FEE_PERCENTAGE=10
MIN_LAUNCH_AMOUNT=1000000000000000000
MAX_TOKEN_SUPPLY=1000000000000000000000000

# Security
JWT_SECRET=your_jwt_secret_here
ADMIN_ADDRESSES=0x1234...,0x5678...

# Development
NODE_ENV=development
DEBUG=true
LOG_LEVEL=info
